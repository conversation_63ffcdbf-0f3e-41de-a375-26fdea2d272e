{"_format": "hh-sol-cache-2", "files": {"/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/contracts/main-contract/KRNL.sol": {"lastModificationDate": 1752694029773, "contentHash": "db7907da67439aa3b107a94504eed235", "sourceName": "contracts/main-contract/KRNL.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["KRNL"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1752693854739, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol": {"lastModificationDate": 1752693854229, "contentHash": "81de029d56aa803972be03c5d277cb6c", "sourceName": "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["ECDSA"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol": {"lastModificationDate": 1752693854734, "contentHash": "260f3968eefa3bbd30520cff5384cd93", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Strings.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["MessageHashUtils"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1752693854046, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/Strings.sol": {"lastModificationDate": 1752693854766, "contentHash": "********************************", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SafeCast.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Strings"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/math/SafeCast.sol": {"lastModificationDate": 1752693854757, "contentHash": "2adca1150f58fc6f3d1f0a0f22ee7cca", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeCast"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"lastModificationDate": 1752693854728, "contentHash": "5ec781e33d3a9ac91ffdc83d94420412", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Panic.sol", "./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Math"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"lastModificationDate": 1752693854761, "contentHash": "ae3528afb8bdb0a7dcfba5b115ee8074", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SignedMath"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@openzeppelin/contracts/utils/Panic.sol": {"lastModificationDate": 1752693854746, "contentHash": "2133dc13536b4a6a98131e431fac59e1", "sourceName": "@openzeppelin/contracts/utils/Panic.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Panic"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/contracts/main-contract/TokenGatedContract.sol": {"lastModificationDate": 1752694069861, "contentHash": "ad3e49935b1ecd80476a9e9d53edadd5", "sourceName": "contracts/main-contract/TokenGatedContract.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./KRNL.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["TokenGatedContract"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/contracts/token-authority/TokenAuthority.sol": {"lastModificationDate": 1752695022455, "contentHash": "2b7be98eaa5b6686ce6ae9eaecbc8341", "sourceName": "contracts/token-authority/TokenAuthority.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "@oasisprotocol/sapphire-contracts/contracts/Sapphire.sol", "@oasisprotocol/sapphire-contracts/contracts/EthereumUtils.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["TokenAuthority"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@oasisprotocol/sapphire-contracts/contracts/Sapphire.sol": {"lastModificationDate": 1752693897192, "contentHash": "6383174350ee5207867e05fb02436181", "sourceName": "@oasisprotocol/sapphire-contracts/contracts/Sapphire.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Sapphire"]}, "/home/<USER>/Documents/GKM/KRNL_Demo/token_authority_gate/node_modules/@oasisprotocol/sapphire-contracts/contracts/EthereumUtils.sol": {"lastModificationDate": 1752693897189, "contentHash": "fc23937e677647b5ae1a5439466b7f8e", "sourceName": "@oasisprotocol/sapphire-contracts/contracts/EthereumUtils.sol", "solcConfig": {"version": "0.8.24", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./Sapphire.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["EthereumUtils"]}}}