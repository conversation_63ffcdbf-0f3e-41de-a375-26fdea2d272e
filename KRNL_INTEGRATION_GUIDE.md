# KRNL Platform Integration Guide

This guide walks you through the complete process of implementing a proper KRNL platform integration following the official smart contract developer workflow.

## 🎉 Integration Status: COMPLETED ✅

This project demonstrates a **fully functional** KRNL integration with:
- **Token Authority Gate Kernel**: ✅ Validates ERC-20 token balance thresholds
- **Token Authority Contract**: ✅ Deployed on Oasis Sapphire testnet (`******************************************`)
- **Protected Smart Contract**: ✅ Deployed on Sepolia testnet (`******************************************`)
- **KRNL Platform Registration**: ✅ Smart Contract ID: 8932, dApp ID: 8591

## Live Deployment Summary

| Component | Network | Address | Status |
|-----------|---------|---------|--------|
| Token Authority | Oasis Sapphire Testnet | `******************************************` | ✅ Deployed & Verified |
| Main Contract | Sepolia Testnet | `******************************************` | ✅ Deployed & Verified |
| KRNL Registration | KRNL Platform | Smart Contract ID: 8932 | ✅ Registered |

## Prerequisites

1. **Node.js** (v16 or higher)
2. **npm** or **yarn**
3. **Private key** with funds on both Sepolia and Oasis testnets
4. **Infura/Alchemy API key** for Ethereum RPC access
5. **Etherscan API key** for contract verification

### Getting Testnet Funds

- **Sepolia ETH**: https://sepoliafaucet.com/
- **Oasis ROSE**: https://faucet.testnet.oasis.dev/

## Step-by-Step Integration

### Step 1: Environment Setup

1. Clone and install:
```bash
git clone <repository-url>
cd token_authority_gate
npm install
```

2. Configure environment:
```bash
cp .env.example .env
```

3. Edit `.env` with your credentials:
```bash
# Kernel RPC Configuration
RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID

# Smart Contract Deployment
PRIVATE_KEY=your_private_key_here
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_PROJECT_ID
ETHERSCAN_API_KEY=your_etherscan_api_key_here
```

### Step 2: Test Kernel Functionality

Before deploying contracts, test the kernel:
```bash
npm run test:smoke
```

Expected output:
```
🧪 Running Token Authority Gate Smoke Test...
📋 Test 1: USDT Balance Check - High Threshold
   ✅ Success (544ms)
   📊 Result:
      Authorized: false
      Actual Balance: 256494952
      Human Readable: 256 USDT
```

### Step 3: Deploy Token Authority (Oasis Testnet)

Deploy the Token Authority contract that will protect your main contract:

```bash
npm run deploy:token-authority
```

This will:
- Deploy `TokenAuthority.sol` to Oasis Sapphire testnet
- Generate cryptographic keypairs for signing
- Configure the token_authority_gate kernel
- Output the public key address

**Important**: Save the public key address from the output - you'll need it for the next step.

### Step 4: Deploy Main Contract (Sepolia Testnet)

Deploy the protected smart contract:

```bash
# Set the Token Authority public key from Step 3
export TOKEN_AUTHORITY_PUBLIC_KEY=0x...

npm run deploy:main-contract
```

Or deploy both contracts in sequence:
```bash
npm run deploy:all
```

### Step 5: Verify Contracts

Verify both contracts on their respective networks:

```bash
npm run verify:all
```

This will:
- Verify Token Authority on Oasis Explorer
- Verify Main Contract on Etherscan (Sepolia)

### Step 6: Register on KRNL Platform

1. **Register the Kernel**:
```bash
# Publish kernel to IPFS
./publish.sh

# Register with KRNL CLI
krnl-cli register \
  --id token_authority_gate \
  --version 0.1.0 \
  --ipfs-uri ipfs://YOUR_CID_HERE \
  --spec-file kernel/kernel-spec.yaml
```

2. **Register Token Authority**: Use the KRNL platform to register your Token Authority contract

3. **Register Main Contract**: Register your protected smart contract on the KRNL platform

## Usage Examples

### Interacting with Protected Functions

The main contract has several protected functions that require KRNL authorization:

```solidity
// Update message (requires token balance validation)
function updateMessage(KrnlPayload memory krnlPayload, string memory newMessage)

// Increment counter (requires token balance validation)  
function incrementCounter(KrnlPayload memory krnlPayload)

// Premium function (requires higher token balance)
function premiumIncrement(KrnlPayload memory krnlPayload, uint256 multiplier)
```

### Using KRNL SDK

```javascript
const { KRNLClient } = require('@krnl/sdk');

const client = new KRNLClient({
  apiKey: 'your-api-key'
});

// Example: Call protected function
async function callProtectedFunction() {
  try {
    // The KRNL SDK will automatically:
    // 1. Execute the token_authority_gate kernel
    // 2. Get authorization from Token Authority
    // 3. Call the protected function with proper payload
    
    const result = await client.callContract({
      contractAddress: 'YOUR_MAIN_CONTRACT_ADDRESS',
      functionName: 'updateMessage',
      args: ['Hello KRNL!'],
      kernels: ['token_authority_gate']
    });
    
    console.log('Transaction successful:', result);
  } catch (error) {
    console.error('Transaction failed:', error.message);
  }
}
```

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   KRNL SDK      │    │ token_authority_ │    │ Token Authority │
│   (dApp)        │───▶│ gate Kernel      │───▶│ (Oasis Testnet) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ Kernel Response  │    │ Signed Auth     │
                       │ (Token Balance)  │    │ Payload         │
                       └──────────────────┘    └─────────────────┘
                                │                        │
                                └────────┬───────────────┘
                                         ▼
                                ┌─────────────────┐
                                │ Protected       │
                                │ Smart Contract  │
                                │ (Sepolia)       │
                                └─────────────────┘
```

## Key Components

### 1. Token Authority Gate Kernel
- **Location**: `kernel/index.js`
- **Function**: Validates ERC-20 token balance thresholds
- **Input**: `{ userAddress, tokenAddress, minBalance }`
- **Output**: `{ authorized: boolean, actualBalance: string }`

### 2. Token Authority Contract
- **Network**: Oasis Sapphire Testnet
- **Purpose**: Confidential validation and signing of kernel responses
- **Key Features**:
  - Cryptographic keypair generation
  - Kernel response validation
  - Access control and authorization

### 3. Protected Smart Contract
- **Network**: Sepolia Testnet
- **Purpose**: Demonstrates token-gated access control
- **Protection**: KRNL modifier validates all protected function calls

## Troubleshooting

### Common Issues

1. **Deployment Fails**:
   - Check you have sufficient funds on both networks
   - Verify RPC URLs are correct
   - Ensure private key is valid

2. **Verification Fails**:
   - Wait a few minutes after deployment
   - Check Etherscan API key is set
   - Verify constructor arguments match

3. **KRNL Integration Issues**:
   - Ensure Token Authority public key is correct
   - Verify kernel is registered on KRNL platform
   - Check contract addresses match registration

### Getting Help

- **KRNL Documentation**: https://docs.krnl.xyz/
- **KRNL Discord**: [Join the community]
- **GitHub Issues**: Report bugs and feature requests

## Security Considerations

1. **Private Key Security**: Never commit private keys to version control
2. **Token Authority**: Consider using a multisig wallet for production
3. **Kernel Validation**: Ensure proper input validation in your kernels
4. **Access Control**: Review Token Authority permissions regularly

## Production Deployment

For production deployment:

1. **Use Mainnet Networks**:
   - Token Authority: Oasis Sapphire Mainnet
   - Main Contract: Ethereum Mainnet

2. **Security Hardening**:
   - Use hardware wallets or multisig
   - Implement additional access controls
   - Conduct security audits

3. **Monitoring**:
   - Set up contract monitoring
   - Monitor kernel performance
   - Track authorization patterns

## Next Steps

1. Customize the Token Authority rules for your use case
2. Implement additional kernels as needed
3. Build your dApp frontend using KRNL SDK
4. Test thoroughly on testnets before mainnet deployment
5. Consider professional security audit for production use
