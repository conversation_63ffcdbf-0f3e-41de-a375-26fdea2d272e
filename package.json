{"name": "token_authority_gate", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test:smoke": "node kernel/test.js", "start": "node kernel/index.js", "compile": "npx hardhat compile", "deploy:token-authority": "npx hardhat run scripts/deployment/deploy-token-authority.js --network sapphire_testnet", "deploy:main-contract": "npx hardhat run scripts/deployment/deploy-main-contract.js --network sepolia", "deploy:all": "node scripts/deployment/deploy-all.js", "verify:oasis": "npx hardhat run scripts/verification/verify-oasis.js --network sapphire_testnet", "verify:sepolia": "npx hardhat run scripts/verification/verify-sepolia.js --network sepolia", "verify:all": "node scripts/verification/verify-all.js", "clean": "npx hardhat clean", "test": "npx hardhat test"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"dotenv": "16.4.5", "ethers": "6.13.4"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.0.0", "@oasisprotocol/sapphire-contracts": "^0.2.14", "@openzeppelin/contracts": "^5.3.0", "hardhat": "^2.25.0"}}