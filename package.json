{"name": "token_authority_gate", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test:smoke": "node kernel/test.js", "start": "node kernel/index.js", "compile": "npx hardhat compile", "deploy:token-authority": "npx hardhat run scripts/deployment/deploy-token-authority.js --network sapphire_testnet", "deploy:main-contract": "npx hardhat run scripts/deployment/deploy-main-contract.js --network sepolia", "deploy:all": "node scripts/deployment/deploy-all.js", "verify:oasis": "npx hardhat run scripts/verification/verify-oasis.js --network sapphire_testnet", "verify:sepolia": "npx hardhat run scripts/verification/verify-sepolia.js --network sepolia", "verify:all": "node scripts/verification/verify-all.js", "clean": "npx hardhat clean", "test": "npx hardhat test"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"dotenv": "16.4.5", "ethers": "6.13.4"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.9", "@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-ignition": "^0.15.12", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.13", "@nomicfoundation/hardhat-network-helpers": "^1.0.13", "@nomicfoundation/hardhat-toolbox": "^6.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@nomicfoundation/ignition-core": "^0.15.12", "@oasisprotocol/sapphire-contracts": "^0.2.14", "@openzeppelin/contracts": "^5.3.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.20", "@types/mocha": "^10.0.10", "chai": "^4.5.0", "hardhat": "^2.25.0", "hardhat-gas-reporter": "^2.3.0", "solidity-coverage": "^0.8.16", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.8.3"}}