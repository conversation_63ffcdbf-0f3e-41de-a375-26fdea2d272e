# KRNL Platform Integration Demo

## Overview

This project demonstrates a complete KRNL platform integration following the official smart contract developer workflow. It includes:

1. **Token Authority Gate Kernel** - An off-chain kernel that validates ERC-20 token balance thresholds
2. **Token Authority Smart Contract** - Deployed on Oasis testnet for confidential access control
3. **Protected Smart Contract** - Main contract deployed on Sepolia testnet with KRNL protection
4. **Complete Integration Workflow** - Following the 8-step KRNL developer process

The Token Authority Gate kernel validates ERC-20 token balance thresholds and returns authorization status, making it useful for token-gated access control, DeFi protocols, and membership verification systems.

## Project Structure

```
token_authority_gate/
├── kernel/                     # KRNL Kernel Implementation
│   ├── index.js               # Token Authority Gate kernel logic
│   ├── test.js                # Kernel smoke tests
│   └── kernel-spec.yaml       # Kernel specification
├── contracts/                  # Smart Contracts
│   ├── token-authority/        # Token Authority (Oasis testnet)
│   │   └── TokenAuthority.sol
│   └── main-contract/          # Protected Contract (Sepolia testnet)
│       ├── KRNL.sol           # KRNL integration base contract
│       └── TokenGatedContract.sol
├── scripts/                    # Deployment & Verification Scripts
│   ├── deployment/
│   │   ├── deploy-token-authority.js
│   │   ├── deploy-main-contract.js
│   │   └── deploy-all.js
│   └── verification/
│       ├── verify-oasis.js
│       ├── verify-sepolia.js
│       └── verify-all.js
├── deployments/               # Deployment records (auto-generated)
├── hardhat.config.js         # Hardhat configuration
└── package.json              # Dependencies and scripts
```

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd token_authority_gate
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment template:
```bash
cp .env.example .env
```

4. Configure your environment variables in `.env`:
```bash
# Kernel RPC Configuration
RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID

# Smart Contract Deployment
PRIVATE_KEY=your_private_key_here
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_PROJECT_ID
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# Token Authority (set after deployment)
TOKEN_AUTHORITY_PUBLIC_KEY=
```

## KRNL Platform Integration Workflow

This project follows the official 8-step KRNL workflow for smart contract developers:

### Step 1-3: Kernel Selection and Rules Definition
✅ **Completed**: The `token_authority_gate` kernel is implemented and configured to validate ERC-20 token balance thresholds.

### Step 4-5: Token Authority Deployment
Deploy the Token Authority contract on Oasis Sapphire testnet:

```bash
npm run deploy:token-authority
```

This will:
- Deploy the TokenAuthority contract to Oasis testnet
- Generate cryptographic keypairs for signing
- Configure the token_authority_gate kernel (ID: 1001)
- Output the public key for use in the main contract

### Step 6-8: Main Contract Integration and Deployment
Deploy the protected smart contract on Sepolia testnet:

```bash
npm run deploy:main-contract
```

This will:
- Deploy the TokenGatedContract with KRNL protection
- Use the Token Authority public key from step 4-5
- Enable token-gated access control for protected functions

### Complete Deployment
Deploy both contracts in the correct order:

```bash
npm run deploy:all
```

## Contract Verification

Verify deployed contracts on their respective networks:

### Verify Token Authority (Oasis)
```bash
npm run verify:oasis
```

### Verify Main Contract (Sepolia)
```bash
npm run verify:sepolia
```

### Verify All Contracts
```bash
npm run verify:all
```

## Testing

### Kernel Testing
Test the token_authority_gate kernel functionality:

```bash
npm run test:smoke
```

The smoke test validates:
- ✅ Real blockchain data retrieval using Vitalik's address
- ✅ USDT token balance checking with different thresholds
- ✅ Proper JSON response structure validation
- ✅ Error handling for invalid inputs
- ✅ Network connectivity and RPC functionality

### Smart Contract Testing
```bash
npm run test
```

Compile contracts:
```bash
npm run compile
```

## KRNL Platform Registration

After deploying contracts, register them on the KRNL platform:

### 1. Register the Kernel
Publish the kernel to IPFS:
```bash
./publish.sh
```

Register with KRNL CLI:
```bash
krnl-cli register \
  --id token_authority_gate \
  --version 0.1.0 \
  --ipfs-uri ipfs://YOUR_CID_HERE \
  --spec-file kernel/kernel-spec.yaml \
  --description "Validates ERC-20 token balance thresholds for authorization"
```

### 2. Register Token Authority
Register the Token Authority contract deployed on Oasis testnet with the KRNL platform.

### 3. Register Main Contract
Register the main protected contract deployed on Sepolia testnet with the KRNL platform.

## Usage

### SDK Integration Example

```javascript
const { KRNLClient } = require('@krnl/sdk');

const client = new KRNLClient({
  apiKey: 'your-api-key'
});

async function checkTokenGate() {
  try {
    const result = await client.execute('token_authority_gate', {
      userAddress: '******************************************',
      tokenAddress: '******************************************', // USDT
      minBalance: '1000000' // 1 USDT (6 decimals)
    });

    console.log('Authorization:', result.authorized);
    console.log('Balance:', result.actualBalance);
    
    if (result.authorized) {
      console.log('✅ User has sufficient token balance');
    } else {
      console.log('❌ User does not meet minimum balance requirement');
    }
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkTokenGate();
```

### Input Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `userAddress` | string | Ethereum address to check | `******************************************` |
| `tokenAddress` | string | ERC-20 token contract address | `******************************************` |
| `minBalance` | string | Minimum balance in smallest unit | `1000000` (1 USDC) |

### Output Format

```json
{
  "authorized": true,
  "actualBalance": "1234567890"
}
```

### Common Token Addresses (Ethereum Mainnet)

| Token | Address | Decimals | Example Balance |
|-------|---------|----------|-----------------|
| USDC | `******************************************` | 6 | `1000000` = 1 USDC |
| USDT | `******************************************` | 6 | `1000000` = 1 USDT |
| DAI | `******************************************` | 18 | `1000000000000000000` = 1 DAI |
| WETH | `******************************************` | 18 | `1000000000000000000` = 1 WETH |

## Error Handling

The kernel provides comprehensive error handling:

- **Invalid Address**: `Invalid userAddress: must be a valid Ethereum address`
- **Missing Inputs**: `Missing required inputs: userAddress, tokenAddress, and minBalance are required`
- **Network Issues**: `Network error: Unable to connect to Ethereum RPC`
- **Contract Errors**: `Contract call failed: Invalid token contract or network issue`

## Development

### File Structure
```
token_authority_gate/
├── kernel-spec.yaml     # Kernel specification
├── index.js            # Main execution logic
├── test.js             # Comprehensive smoke tests
├── package.json        # Dependencies and scripts
├── .env.example        # Environment template
├── .gitignore          # Git ignore rules
├── publish.sh          # IPFS deployment script
└── README.md           # This documentation
```

### Local Development

1. Set up environment:
```bash
cp .env.example .env
# Edit .env with your RPC URL
```

2. Run tests:
```bash
npm run test:smoke
```

3. Test with custom parameters:
```bash
node -e "
const { execute } = require('./index.js');
execute({
  userAddress: 'YOUR_ADDRESS',
  tokenAddress: 'TOKEN_CONTRACT',
  minBalance: 'MIN_BALANCE'
}).then(console.log).catch(console.error);
"
```

## License

ISC License - See package.json for details.
