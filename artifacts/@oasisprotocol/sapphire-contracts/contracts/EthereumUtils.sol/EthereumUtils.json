{"_format": "hh-sol-artifact-1", "contractName": "EthereumUtils", "sourceName": "@oasisprotocol/sapphire-contracts/contracts/EthereumUtils.sol", "abi": [{"inputs": [], "name": "DER_Split_Error", "type": "error"}, {"inputs": [], "name": "expmod_Error", "type": "error"}, {"inputs": [], "name": "k256Decompress_Invalid_Length_Error", "type": "error"}, {"inputs": [], "name": "k256DeriveY_Invalid_Prefix_Error", "type": "error"}, {"inputs": [], "name": "recoverV_Error", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea26469706673582212205f507ad6585471c861b2723d3f68497b4b475605db65d154c08410444f53265f64736f6c63430008180033", "deployedBytecode": "0x600080fdfea26469706673582212205f507ad6585471c861b2723d3f68497b4b475605db65d154c08410444f53265f64736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}