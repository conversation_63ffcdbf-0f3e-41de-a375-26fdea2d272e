{"_format": "hh-sol-artifact-1", "contractName": "MessageHashUtils", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "abi": [], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea26469706673582212205afa8dce2c63e8a5d7578c666897dd001391dac250df7176a7fd3d356053890764736f6c63430008180033", "deployedBytecode": "0x600080fdfea26469706673582212205afa8dce2c63e8a5d7578c666897dd001391dac250df7176a7fd3d356053890764736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}