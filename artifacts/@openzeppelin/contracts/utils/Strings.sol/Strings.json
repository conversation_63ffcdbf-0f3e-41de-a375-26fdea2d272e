{"_format": "hh-sol-artifact-1", "contractName": "Strings", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "StringsInsufficientHexLength", "type": "error"}, {"inputs": [], "name": "StringsInvalidAddressFormat", "type": "error"}, {"inputs": [], "name": "StringsInvalidChar", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220e1452f39911219f32cd0bf54bcab29b5cdd5f81a5bb9297bb72cff93475c6fb564736f6c63430008180033", "deployedBytecode": "0x600080fdfea2646970667358221220e1452f39911219f32cd0bf54bcab29b5cdd5f81a5bb9297bb72cff93475c6fb564736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}