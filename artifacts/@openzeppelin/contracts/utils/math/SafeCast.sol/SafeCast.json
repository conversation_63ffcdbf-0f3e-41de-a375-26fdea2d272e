{"_format": "hh-sol-artifact-1", "contractName": "SafeCast", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "abi": [{"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "int256", "name": "value", "type": "int256"}], "name": "SafeCastOverflowedIntDowncast", "type": "error"}, {"inputs": [{"internalType": "int256", "name": "value", "type": "int256"}], "name": "SafeCastOverflowedIntToUint", "type": "error"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintDowncast", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintToInt", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea26469706673582212202e098a98f74c1e6dd1f85ca476023c8aca86f42fe4998906afaf05c2d14039e764736f6c63430008180033", "deployedBytecode": "0x600080fdfea26469706673582212202e098a98f74c1e6dd1f85ca476023c8aca86f42fe4998906afaf05c2d14039e764736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}