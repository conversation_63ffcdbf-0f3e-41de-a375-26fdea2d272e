{"_format": "hh-sol-artifact-1", "contractName": "KRNL", "sourceName": "contracts/main-contract/KRNL.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_tokenAuthorityPublicKey", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "UnauthorizedTransaction", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>ey", "type": "address"}], "name": "TokenAuthorityUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "dataDigest", "type": "bytes32"}], "name": "TransactionAuthorized", "type": "event"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "executed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTokenAuthorityPublicKey", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "signatureToken", "type": "bytes"}], "name": "isExecuted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenAuthorityPublicKey", "type": "address"}], "name": "setTokenAuthorityPublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "tokenAuthorityPublicKey", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}