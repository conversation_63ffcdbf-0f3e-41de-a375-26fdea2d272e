{"_format": "hh-sol-artifact-1", "contractName": "TokenGatedContract", "sourceName": "contracts/main-contract/TokenGatedContract.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_tokenAuthorityPublicKey", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "UnauthorizedTransaction", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokenBalance", "type": "uint256"}], "name": "CounterIncremented", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "string", "name": "newMessage", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "tokenBalance", "type": "uint256"}], "name": "MessageUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>ey", "type": "address"}], "name": "TokenAuthorityUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "dataDigest", "type": "bytes32"}], "name": "TransactionAuthorized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "score", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "UserScoreUpdated", "type": "event"}, {"inputs": [], "name": "emergencyReset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "executed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractStats", "outputs": [{"internalType": "string", "name": "", "type": "string"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "get<PERSON>ounter", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMessage", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTokenAuthorityPublicKey", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserLastAccess", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserScore", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "bytes", "name": "auth", "type": "bytes"}, {"internalType": "bytes", "name": "kernelResponses", "type": "bytes"}, {"internalType": "bytes", "name": "kernelParams", "type": "bytes"}], "internalType": "struct KrnlPayload", "name": "krnlPayload", "type": "tuple"}], "name": "incrementCounter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "signatureToken", "type": "bytes"}], "name": "isExecuted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "bytes", "name": "auth", "type": "bytes"}, {"internalType": "bytes", "name": "kernelResponses", "type": "bytes"}, {"internalType": "bytes", "name": "kernelParams", "type": "bytes"}], "internalType": "struct KrnlPayload", "name": "krnlPayload", "type": "tuple"}, {"internalType": "uint256", "name": "multiplier", "type": "uint256"}], "name": "premiumIncrement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenAuthorityPublicKey", "type": "address"}], "name": "setTokenAuthorityPublicKey", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "tokenAuthorityPublicKey", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "bytes", "name": "auth", "type": "bytes"}, {"internalType": "bytes", "name": "kernelResponses", "type": "bytes"}, {"internalType": "bytes", "name": "kernelParams", "type": "bytes"}], "internalType": "struct KrnlPayload", "name": "krnlPayload", "type": "tuple"}, {"internalType": "string", "name": "newMessage", "type": "string"}], "name": "updateMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}