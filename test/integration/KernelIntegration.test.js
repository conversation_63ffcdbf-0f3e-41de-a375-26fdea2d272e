const { expect } = require("chai");
const fs = require("fs");
const path = require("path");

describe("Kernel Integration Tests", function () {
  let kernelModule;
  let testModule;

  before(async function () {
    // Load the kernel modules
    const kernelPath = path.join(__dirname, "..", "..", "kernel", "index.js");
    const testPath = path.join(__dirname, "..", "..", "kernel", "test.js");
    
    if (fs.existsSync(kernelPath)) {
      kernelModule = require(kernelPath);
    }
    
    if (fs.existsSync(testPath)) {
      testModule = require(testPath);
    }
  });

  describe("Kernel Module Structure", function () {
    it("Should have kernel specification file", function () {
      const specPath = path.join(__dirname, "..", "..", "kernel", "kernel-spec.yaml");
      expect(fs.existsSync(specPath)).to.be.true;
      
      const specContent = fs.readFileSync(specPath, "utf8");
      expect(specContent).to.include("id: token_authority_gate");
      expect(specContent).to.include("token_authority_gate");
    });

    it("Should have main kernel implementation", function () {
      const kernelPath = path.join(__dirname, "..", "..", "kernel", "index.js");
      expect(fs.existsSync(kernelPath)).to.be.true;
    });

    it("Should have kernel test file", function () {
      const testPath = path.join(__dirname, "..", "..", "kernel", "test.js");
      expect(fs.existsSync(testPath)).to.be.true;
    });
  });

  describe("Kernel Functionality", function () {
    it("Should export required kernel functions", function () {
      if (kernelModule) {
        // Check if the kernel module exports the expected functions
        expect(typeof kernelModule).to.be.oneOf(["function", "object"]);
      }
    });

    it("Should handle token balance checking logic", function () {
      // This would test the actual kernel logic
      // For now, we verify the structure is in place
      const kernelPath = path.join(__dirname, "..", "..", "kernel", "index.js");
      const kernelContent = fs.readFileSync(kernelPath, "utf8");
      
      // Check that the kernel contains token balance checking logic
      expect(kernelContent).to.include("balance");
      expect(kernelContent).to.include("token");
    });
  });

  describe("Kernel Configuration", function () {
    it("Should have proper environment configuration", function () {
      // Check that RPC_URL is configured for token balance checking
      const envPath = path.join(__dirname, "..", "..", ".env");
      if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, "utf8");
        expect(envContent).to.include("RPC_URL");
      }
    });

    it("Should have kernel payload template", function () {
      const payloadPath = path.join(__dirname, "..", "..", "kernel-payload.json");
      expect(fs.existsSync(payloadPath)).to.be.true;
      
      const payload = JSON.parse(fs.readFileSync(payloadPath, "utf8"));
      expect(payload.kernelPayload).to.have.property("1001");
    });
  });

  describe("KRNL Platform Integration", function () {
    it("Should have KRNL registration information", function () {
      // Verify that KRNL registration was successful
      expect(process.env.TOKEN_AUTHORITY_ADDRESS).to.not.be.undefined;
      expect(process.env.MAIN_CONTRACT_ADDRESS).to.not.be.undefined;
    });

    it("Should have proper kernel ID configuration", function () {
      expect(process.env.KRNL_KERNEL_IDS).to.equal("1001");
    });
  });

  describe("End-to-End Workflow", function () {
    it("Should have all components for complete KRNL integration", function () {
      // Check that all necessary files exist for the complete workflow
      const requiredFiles = [
        "kernel/index.js",
        "kernel/kernel-spec.yaml", 
        "kernel-payload.json",
        "contracts/token-authority/TokenAuthority.sol",
        "contracts/main-contract/TokenGatedContract.sol",
        "deployments/token-authority-oasis.json",
        "deployments/main-contract-sepolia.json"
      ];

      requiredFiles.forEach(file => {
        const filePath = path.join(__dirname, "..", "..", file);
        expect(fs.existsSync(filePath)).to.be.true;
      });
    });

    it("Should have deployment information consistency", function () {
      const taDeploymentPath = path.join(__dirname, "..", "..", "deployments", "token-authority-oasis.json");
      const mcDeploymentPath = path.join(__dirname, "..", "..", "deployments", "main-contract-sepolia.json");
      
      if (fs.existsSync(taDeploymentPath) && fs.existsSync(mcDeploymentPath)) {
        const taInfo = JSON.parse(fs.readFileSync(taDeploymentPath, "utf8"));
        const mcInfo = JSON.parse(fs.readFileSync(mcDeploymentPath, "utf8"));
        
        // Verify that the main contract uses the correct Token Authority public key
        expect(mcInfo.tokenAuthorityPublicKey).to.equal(taInfo.publicKeyAddress);
      }
    });
  });
});
