const { expect } = require("chai");
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

describe("KRNL Integration Tests", function () {
  let tokenAuthority;
  let tokenGatedContract;
  let owner;
  let user;
  let tokenAuthorityAddress;
  let mainContractAddress;
  let deploymentInfo;

  before(async function () {
    // Get signers
    [owner, user] = await ethers.getSigners();
    
    // Load deployment information
    const deploymentDir = path.join(__dirname, "..", "..", "deployments");
    
    // Load Token Authority deployment info
    const taDeploymentFile = path.join(deploymentDir, "token-authority-oasis.json");
    if (fs.existsSync(taDeploymentFile)) {
      const taInfo = JSON.parse(fs.readFileSync(taDeploymentFile, "utf8"));
      tokenAuthorityAddress = taInfo.contractAddress;
    }
    
    // Load Main Contract deployment info
    const mcDeploymentFile = path.join(deploymentDir, "main-contract-sepolia.json");
    if (fs.existsSync(mcDeploymentFile)) {
      const mcInfo = JSON.parse(fs.readFileSync(mcDeploymentFile, "utf8"));
      mainContractAddress = mcInfo.contractAddress;
      deploymentInfo = mcInfo;
    }

    console.log("Token Authority Address:", tokenAuthorityAddress);
    console.log("Main Contract Address:", mainContractAddress);
  });

  describe("Contract Deployment Verification", function () {
    it("Should have valid deployment addresses", function () {
      expect(tokenAuthorityAddress).to.be.properAddress;
      expect(mainContractAddress).to.be.properAddress;
    });

    it("Should load deployment information correctly", function () {
      expect(deploymentInfo).to.not.be.undefined;
      expect(deploymentInfo.contractAddress).to.equal(mainContractAddress);
      expect(deploymentInfo.network).to.equal("sepolia");
    });
  });

  describe("Token Authority Contract Tests", function () {
    it("Should connect to deployed Token Authority contract", async function () {
      // Note: This test would need to connect to Oasis Sapphire testnet
      // For now, we'll test the contract interface
      const TokenAuthority = await ethers.getContractFactory("TokenAuthority");
      expect(TokenAuthority).to.not.be.undefined;
    });
  });

  describe("Main Contract Tests", function () {
    beforeEach(async function () {
      // Connect to the deployed contract on Sepolia
      // For testing purposes, we'll deploy a local version
      const TokenGatedContract = await ethers.getContractFactory("TokenGatedContract");
      
      // Use the actual Token Authority public key from deployment
      const tokenAuthorityPublicKey = deploymentInfo?.tokenAuthorityPublicKey || owner.address;
      
      tokenGatedContract = await TokenGatedContract.deploy(tokenAuthorityPublicKey);
      await tokenGatedContract.waitForDeployment();
    });

    it("Should deploy with correct Token Authority public key", async function () {
      const deployedTAKey = await tokenGatedContract.getTokenAuthorityPublicKey();
      expect(deployedTAKey).to.be.properAddress;
    });

    it("Should have correct initial state", async function () {
      const message = await tokenGatedContract.getMessage();
      const counter = await tokenGatedContract.getCounter();
      const contractOwner = await tokenGatedContract.owner();

      expect(message).to.equal("Welcome to KRNL Token-Gated Contract");
      expect(counter).to.equal(0);
      expect(contractOwner).to.equal(owner.address);
    });

    it("Should allow public functions without KRNL authorization", async function () {
      const message = await tokenGatedContract.getMessage();
      expect(message).to.equal("Welcome to KRNL Token-Gated Contract");

      const counter = await tokenGatedContract.getCounter();
      expect(counter).to.equal(0);
    });

    it("Should reject protected functions without proper authorization", async function () {
      const mockKrnlPayload = {
        auth: "0x1234",
        kernelResponses: "0x5678",
        kernelParams: "0x9abc"
      };

      await expect(
        tokenGatedContract.updateMessage(mockKrnlPayload, "New Message")
      ).to.be.reverted;
    });

    it("Should have proper access control for owner functions", async function () {
      // Test that only owner can call owner-only functions
      await expect(
        tokenGatedContract.connect(user).emergencyReset()
      ).to.be.revertedWithCustomError(tokenGatedContract, "OwnableUnauthorizedAccount");
    });
  });

  describe("KRNL Integration Workflow", function () {
    it("Should have kernel payload configuration", function () {
      const kernelPayloadFile = path.join(__dirname, "..", "..", "kernel-payload.json");
      expect(fs.existsSync(kernelPayloadFile)).to.be.true;
      
      const kernelPayload = JSON.parse(fs.readFileSync(kernelPayloadFile, "utf8"));
      expect(kernelPayload.kernelPayload).to.have.property("1001");
    });

    it("Should have proper KRNL registration data", function () {
      // Check that we have the necessary KRNL registration information
      expect(process.env.TOKEN_AUTHORITY_ADDRESS).to.not.be.undefined;
      expect(process.env.MAIN_CONTRACT_ADDRESS).to.not.be.undefined;
      expect(process.env.TOKEN_AUTHORITY_PUBLIC_KEY).to.not.be.undefined;
    });
  });

  describe("Contract Interface Validation", function () {
    it("Should have all required KRNL integration functions", async function () {
      const TokenGatedContract = await ethers.getContractFactory("TokenGatedContract");
      const contract = await TokenGatedContract.deploy(owner.address);

      // Check that all KRNL-required functions exist
      expect(contract.interface.getFunction("updateMessage")).to.not.be.null;
      expect(contract.interface.getFunction("incrementCounter")).to.not.be.null;
      expect(contract.interface.getFunction("getMessage")).to.not.be.null;
      expect(contract.interface.getFunction("getCounter")).to.not.be.null;
      expect(contract.interface.getFunction("premiumIncrement")).to.not.be.null;
    });

    it("Should have proper function signatures for KRNL integration", async function () {
      const TokenGatedContract = await ethers.getContractFactory("TokenGatedContract");
      const contract = await TokenGatedContract.deploy(owner.address);

      // Verify that protected functions have the correct KRNL signature
      const updateMessageFunc = contract.interface.getFunction("updateMessage");
      expect(updateMessageFunc.inputs).to.have.lengthOf(2); // KrnlPayload + message

      const incrementCounterFunc = contract.interface.getFunction("incrementCounter");
      expect(incrementCounterFunc.inputs).to.have.lengthOf(1); // KrnlPayload only
    });
  });
});
