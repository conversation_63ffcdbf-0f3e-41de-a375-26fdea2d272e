{"network": "sapphire_testnet", "contractAddress": "0x53815508558bF029ecBE190A4631876783ac27e6", "publicKeyAddress": "0x337cB39FF8D1d3F628B7C0a393794fb806617154", "publicKeyBytes": "0x028f5fad6e0bb93afacef74a5a53456ec7bf580c0218a961ab4f6f663841b8df63", "deployerAddress": "0x842d803eB7d05D6Aa2DdB8c3Eb912e6d97ce31C4", "deploymentTimestamp": "2025-07-16T19:46:50.646Z", "transactionHash": "0x20e24c33c095341b332a691b07428d0b161506221e8de0342836ad658ff6defe", "blockNumber": 12598945, "verified": true, "verificationTimestamp": "2025-07-16T19:51:30.777Z", "explorerUrl": "https://explorer.sapphire.oasis.dev/address/0x53815508558bF029ecBE190A4631876783ac27e6", "verificationTests": {"ownerAccessible": true, "signingKeypairAccessible": true, "publicKeyMatches": true, "contractFunctional": true, "bytecodePresent": true}}